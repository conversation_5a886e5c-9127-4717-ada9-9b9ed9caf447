import time
import traceback

import random
import string
from DrissionPage.errors import PageDisconnectedError
from flask import Flask, jsonify

from driver import ProxyDriver


app = Flask(__name__)
proxy_driver = ProxyDriver()



# 成功返回的封装函数
def success_response(data=None, message="Success", time_use=0, cookies=None):
    if cookies is None:
        cookies = ""
    return jsonify({
        "message": message,
        "data": data,
        "cookies": cookies,
        "time_use": time_use
    })


def error_response(message="Error", data=None, code=400):
    return jsonify({
        "status": "error",
        "message": message,
        "data": data
    }), code







@app.get('/get_cookie')
def search():
    try:
        random_letter = "".join([random.choice(string.ascii_lowercase) for _ in range(random.randrange(3, 5))])
        url = f"https://www.google.com/search?client=aff-cs-360se&ie=UTF-8&q=site%3A{random_letter}.com&oq=site%3A{random_letter}.com&aqs=chrome..69i57j69i59l2j69i60j69i58j69i60l2.5669j0j1&"
        start_time = time.time()
        cookies = proxy_driver.generate_cookie(url)
        return success_response(time_use=int(time.time()*1000) - int(start_time*1000),
                                cookies=cookies)
    except PageDisconnectedError as e:
        return error_response(
            message=str(e), code=501
        )
    except Exception:
        print(traceback.format_exc())
        return error_response(
            message=traceback.format_exc(), code=500
        )


if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
