#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import time
import logging
from datetime import datetime
from flask import Flask, jsonify, request
from flask_cors import CORS
import threading
from playwright_driver import GoogleCookieService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建 Flask 应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局服务实例
cookie_service = None
service_lock = threading.Lock()

def get_service():
    """获取或创建 Cookie 服务实例"""
    global cookie_service
    with service_lock:
        if cookie_service is None:
            try:
                cookie_service = GoogleCookieService()
                logger.info("Google Cookie Service initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize service: {e}")
                raise
        return cookie_service

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        service = get_service()
        health_status = service.health_check()
        
        return jsonify({
            "status": "ok",
            "service": health_status,
            "timestamp": datetime.now().isoformat(),
            "uptime": time.time()
        }), 200
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            "status": "error",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

@app.route('/api/cookies', methods=['GET'])
def get_cookies():
    """获取 Google Cookies"""
    try:
        # 获取查询参数
        target_url = request.args.get('url', 'https://www.google.com')
        fresh_session = request.args.get('fresh', 'false').lower() == 'true'
        
        service = get_service()
        
        # 根据参数选择获取方式
        if fresh_session:
            result = service.get_fresh_session_cookies()
        else:
            result = service.get_google_cookies(target_url)
        
        if result['success']:
            logger.info(f"Successfully retrieved {result['count']} cookies")
            return jsonify({
                "status": "success",
                "data": result,
                "request_id": f"req_{int(time.time())}"
            }), 200
        else:
            logger.warning(f"Failed to get cookies: {result.get('error', 'Unknown error')}")
            return jsonify({
                "status": "error",
                "error": result.get('error', 'Unknown error'),
                "request_id": f"req_{int(time.time())}"
            }), 400
            
    except Exception as e:
        logger.error(f"Error in get_cookies: {e}")
        return jsonify({
            "status": "error",
            "error": str(e),
            "request_id": f"req_{int(time.time())}"
        }), 500

@app.route('/api/cookies/search', methods=['GET'])
def get_search_cookies():
    """通过搜索获取 Google Cookies"""
    try:
        # 获取搜索查询参数
        query = request.args.get('q', 'test')
        
        service = get_service()
        result = service.get_cookies_for_search(query)
        
        if result['success']:
            logger.info(f"Successfully retrieved search cookies for query: {query}")
            return jsonify({
                "status": "success",
                "data": result,
                "query": query,
                "request_id": f"req_{int(time.time())}"
            }), 200
        else:
            logger.warning(f"Failed to get search cookies: {result.get('error', 'Unknown error')}")
            return jsonify({
                "status": "error",
                "error": result.get('error', 'Unknown error'),
                "query": query,
                "request_id": f"req_{int(time.time())}"
            }), 400
            
    except Exception as e:
        logger.error(f"Error in get_search_cookies: {e}")
        return jsonify({
            "status": "error",
            "error": str(e),
            "request_id": f"req_{int(time.time())}"
        }), 500

@app.route('/api/cookies/string', methods=['GET'])
def get_cookie_string():
    """只返回 Cookie 字符串格式（便于直接使用）"""
    try:
        target_url = request.args.get('url', 'https://www.google.com')
        fresh_session = request.args.get('fresh', 'false').lower() == 'true'
        
        service = get_service()
        
        if fresh_session:
            result = service.get_fresh_session_cookies()
        else:
            result = service.get_google_cookies(target_url)
        
        if result['success']:
            return result['cookie_string'], 200, {'Content-Type': 'text/plain'}
        else:
            return f"Error: {result.get('error', 'Unknown error')}", 400, {'Content-Type': 'text/plain'}
            
    except Exception as e:
        logger.error(f"Error in get_cookie_string: {e}")
        return f"Error: {str(e)}", 500, {'Content-Type': 'text/plain'}

@app.route('/api/info', methods=['GET'])
def get_service_info():
    """获取服务信息"""
    return jsonify({
        "service_name": "Google Cookie Service",
        "version": "1.0.0",
        "endpoints": {
            "health": "/health",
            "cookies": "/api/cookies",
            "search_cookies": "/api/cookies/search", 
            "cookie_string": "/api/cookies/string",
            "info": "/api/info"
        },
        "parameters": {
            "/api/cookies": {
                "url": "Target Google URL (optional, default: https://www.google.com)",
                "fresh": "Get fresh session cookies (optional, default: false)"
            },
            "/api/cookies/search": {
                "q": "Search query (optional, default: test)"
            },
            "/api/cookies/string": {
                "url": "Target Google URL (optional)",
                "fresh": "Get fresh session cookies (optional)"
            }
        },
        "examples": {
            "basic_cookies": "/api/cookies",
            "fresh_cookies": "/api/cookies?fresh=true",
            "search_cookies": "/api/cookies/search?q=python",
            "cookie_string": "/api/cookies/string",
            "custom_url": "/api/cookies?url=https://www.google.com/search?q=test"
        }
    })

@app.errorhandler(404)
def not_found(error):
    """404 错误处理"""
    return jsonify({
        "status": "error",
        "error": "Endpoint not found",
        "available_endpoints": [
            "/health",
            "/api/cookies",
            "/api/cookies/search",
            "/api/cookies/string",
            "/api/info"
        ]
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500 错误处理"""
    logger.error(f"Internal server error: {error}")
    return jsonify({
        "status": "error",
        "error": "Internal server error",
        "timestamp": datetime.now().isoformat()
    }), 500

def cleanup_service():
    """清理服务资源"""
    global cookie_service
    if cookie_service:
        try:
            cookie_service.close()
            logger.info("Service cleaned up successfully")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

if __name__ == '__main__':
    import atexit
    import signal
    import sys
    
    # 注册清理函数
    atexit.register(cleanup_service)
    
    def signal_handler(sig, frame):
        logger.info("Received shutdown signal, cleaning up...")
        cleanup_service()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动服务
    logger.info("Starting Google Cookie Service...")
    try:
        # 预初始化服务
        get_service()
        logger.info("Service pre-initialized successfully")
        
        # 启动 Flask 应用
        app.run(
            host='0.0.0.0',  # 允许外部访问
            port=5000,
            debug=False,     # 生产环境关闭调试
            threaded=True    # 支持多线程
        )
    except Exception as e:
        logger.error(f"Failed to start service: {e}")
        cleanup_service()
        sys.exit(1)
