import os
import traceback

from DrissionPage import ChromiumOptions, Chromium
import string

from DrissionPage.errors import PageDisconnectedError, StorageError


class ProxyDriver:
    def __init__(self, proxy='juming:juming123456...@************:32188'):
        self.proxy = proxy
        self.driver = self.create_driver()
    def create_proxy_auth_extension(self, proxy_host, proxy_port, proxy_username, proxy_password, scheme='http',
                                    plugin_path=None):
        # 创建Chrome插件的manifest.json文件内容
        manifest_json = """
           {
               "version": "1.0.0",
               "manifest_version": 3,
               "name": "Chrome Proxy %s",
               "permissions": [
                   "proxy",
                   "storage",
                   "webRequest",
                   "webRequestAuthProvider"
               ],
               "host_permissions": [
                   "<all_urls>"
               ],
               "background": {
                   "service_worker": "background.js"
               }
           }
           """ % f'{proxy_host.replace(".", "_")}_{proxy_port}'

        # 创建Chrome插件的background.js文件内容
        background_js = """
          chrome.runtime.onInstalled.addListener(() => {
              chrome.proxy.settings.set(
                  { value: {
                      mode: "fixed_servers",
                      rules: {
                          singleProxy: {
                              scheme: "http",
                              host: "%s",
                              port: parseInt(%s)
                          },
                          bypassList: ["localhost", "127.0.0.1"]
                      }
                  }, scope: "regular" },
                  () => {
                      console.log("Proxy settings applied");
                  }
              );
          });

          chrome.webRequest.onAuthRequired.addListener(
              (details) => {
                  return {
                      authCredentials: {
                          username: "%s",
                          password: "%s"
                      }
                  };
              },
              { urls: ["<all_urls>"] },
              ["blocking"]
          );
          """ % (proxy_host, proxy_port, proxy_username, proxy_password)

        # 创建插件目录并写入manifest.json和background.js文件
        os.makedirs(plugin_path, exist_ok=True)
        with open(os.path.join(plugin_path, "manifest.json"), "w+") as f:
            f.write(manifest_json)
        with open(os.path.join(plugin_path, "background.js"), "w+") as f:
            f.write(background_js)

        return os.path.join(plugin_path)

    def from_proxy_get_daili(self, proxy):
        # proxy是这种格式 user:pass@ip:port
        user_pass_str, ip_port_str = proxy.split('@')
        proxyHost, proxyPort = ip_port_str.split(':')
        proxyUser, proxyPass = user_pass_str.split(':')
        return proxyHost, proxyPort, proxyUser, proxyPass

    def create_driver(self):
        proxyHost, proxyPort, proxyUser, proxyPass = self.from_proxy_get_daili(self.proxy)
        proxy_auth_plugin_path = self.create_proxy_auth_extension(
            plugin_path="/tmp/google_proxy",
            proxy_host=proxyHost,
            proxy_port=proxyPort,
            proxy_username=proxyUser,
            proxy_password=proxyPass
        )
        option = ChromiumOptions()
        option.no_imgs().auto_port()
        # option.set_argument("--headless")
        option.set_argument('--no-sandbox')
        option.set_argument("--disable-gpu")
        option.set_browser_path(r'/opt/google/chrome/google-chrome')  # 设置chrome浏览器路径

        option.set_user_agent(
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.6367.118 Safari/537.36")
        # option.add_extension(path=proxy_auth_plugin_path)

        drive = Chromium(option)

        return drive



    def generate_cookie(self,url):
        try:
            table = self.driver.new_tab()
        except PageDisconnectedError as e:
            self.close()
            self.driver = self.create_driver()
            table = self.driver.new_tab()
        try:
            self.driver.clear_cache()
            table.get(url, timeout=10)
            print(table.html)
            if not table.cookies(False):
                raise PageDisconnectedError("未获取到cookie")
            return  ";".join([f"{cookie['name']}={cookie['value']}" for cookie in table.cookies(False)])
        except Exception as e:
            raise PageDisconnectedError(e)
        finally:
            table.close()


    def close(self):
        try:
            self.driver.quit(force=True)
        except Exception:
            pass



if __name__ == '__main__':
    driver = ProxyDriver()
    cookies = driver.generate_cookie("https://www.google.com/search?q=qq")

    print(cookies)
