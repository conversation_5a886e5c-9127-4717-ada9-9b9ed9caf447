#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
import time

class GoogleCookieClient:
    """Google Cookie 服务客户端"""
    
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url.rstrip('/')
        
    def health_check(self):
        """检查服务健康状态"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=10)
            return response.json()
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def get_cookies(self, url=None, fresh=False):
        """获取 Google Cookies"""
        params = {}
        if url:
            params['url'] = url
        if fresh:
            params['fresh'] = 'true'
            
        try:
            response = requests.get(f"{self.base_url}/api/cookies", params=params, timeout=30)
            return response.json()
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def get_search_cookies(self, query="test"):
        """通过搜索获取 Cookies"""
        try:
            response = requests.get(
                f"{self.base_url}/api/cookies/search", 
                params={"q": query}, 
                timeout=30
            )
            return response.json()
        except Exception as e:
            return {"status": "error", "error": str(e)}
    
    def get_cookie_string(self, url=None, fresh=False):
        """获取 Cookie 字符串"""
        params = {}
        if url:
            params['url'] = url
        if fresh:
            params['fresh'] = 'true'
            
        try:
            response = requests.get(f"{self.base_url}/api/cookies/string", params=params, timeout=30)
            if response.status_code == 200:
                return response.text
            else:
                return f"Error: {response.text}"
        except Exception as e:
            return f"Error: {str(e)}"
    
    def get_service_info(self):
        """获取服务信息"""
        try:
            response = requests.get(f"{self.base_url}/api/info", timeout=10)
            return response.json()
        except Exception as e:
            return {"status": "error", "error": str(e)}


def demo_usage():
    """演示客户端使用方法"""
    print("=== Google Cookie 服务客户端演示 ===\n")
    
    # 创建客户端
    client = GoogleCookieClient()
    
    # 1. 健康检查
    print("1. 健康检查:")
    health = client.health_check()
    print(json.dumps(health, indent=2, ensure_ascii=False))
    
    if health.get("status") != "ok":
        print("❌ 服务不可用，请检查服务是否启动")
        return
    
    print("✅ 服务正常运行\n")
    
    # 2. 获取基础 Cookies
    print("2. 获取基础 Cookies:")
    cookies_result = client.get_cookies()
    if cookies_result.get("status") == "success":
        data = cookies_result["data"]
        print(f"✅ 成功获取 {data['count']} 个 Cookies")
        print(f"Cookie 字符串长度: {len(data['cookie_string'])}")
    else:
        print(f"❌ 获取失败: {cookies_result.get('error')}")
    print()
    
    # 3. 获取搜索 Cookies
    print("3. 获取搜索 Cookies:")
    search_result = client.get_search_cookies("python programming")
    if search_result.get("status") == "success":
        data = search_result["data"]
        print(f"✅ 成功获取搜索 Cookies: {data['count']} 个")
    else:
        print(f"❌ 获取失败: {search_result.get('error')}")
    print()
    
    # 4. 获取 Cookie 字符串
    print("4. 获取 Cookie 字符串:")
    cookie_string = client.get_cookie_string()
    if not cookie_string.startswith("Error:"):
        print(f"✅ Cookie 字符串: {cookie_string[:100]}...")
    else:
        print(f"❌ {cookie_string}")
    print()
    
    # 5. 获取全新会话 Cookies
    print("5. 获取全新会话 Cookies:")
    fresh_result = client.get_cookies(fresh=True)
    if fresh_result.get("status") == "success":
        data = fresh_result["data"]
        print(f"✅ 成功获取全新会话 Cookies: {data['count']} 个")
    else:
        print(f"❌ 获取失败: {fresh_result.get('error')}")
    print()
    
    # 6. 服务信息
    print("6. 服务信息:")
    info = client.get_service_info()
    if "service_name" in info:
        print(f"✅ 服务名称: {info['service_name']}")
        print(f"版本: {info['version']}")
        print("可用端点:")
        for endpoint in info['endpoints']:
            print(f"  - {endpoint}: {info['endpoints'][endpoint]}")
    else:
        print(f"❌ 获取服务信息失败: {info.get('error')}")


def performance_test():
    """性能测试"""
    print("\n=== 性能测试 ===")
    
    client = GoogleCookieClient()
    
    # 测试多次请求的响应时间
    test_count = 5
    total_time = 0
    success_count = 0
    
    print(f"执行 {test_count} 次 Cookie 获取测试...")
    
    for i in range(test_count):
        start_time = time.time()
        result = client.get_cookies()
        end_time = time.time()
        
        request_time = end_time - start_time
        total_time += request_time
        
        if result.get("status") == "success":
            success_count += 1
            print(f"请求 {i+1}: ✅ {request_time:.2f}s")
        else:
            print(f"请求 {i+1}: ❌ {request_time:.2f}s - {result.get('error')}")
    
    if success_count > 0:
        avg_time = total_time / test_count
        success_rate = (success_count / test_count) * 100
        print(f"\n📊 性能统计:")
        print(f"平均响应时间: {avg_time:.2f}s")
        print(f"成功率: {success_rate:.1f}%")
        print(f"总耗时: {total_time:.2f}s")


if __name__ == "__main__":
    # 基础演示
    demo_usage()
    
    # 性能测试
    performance_test()
    
    print("\n=== 使用示例 ===")
    print("""
# 基础使用
client = GoogleCookieClient("http://localhost:5000")

# 获取 Cookies
cookies = client.get_cookies()
if cookies["status"] == "success":
    cookie_string = cookies["data"]["cookie_string"]
    # 在请求中使用
    headers = {"Cookie": cookie_string}

# 获取搜索相关 Cookies  
search_cookies = client.get_search_cookies("python")

# 直接获取 Cookie 字符串
cookie_string = client.get_cookie_string()

# 获取全新会话 Cookies
fresh_cookies = client.get_cookies(fresh=True)
    """)
