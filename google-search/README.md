
# 部署
## 安装pyenv
1. 下载pyenv
```shell
# github
curl -L https://github.com/pyenv/pyenv-installer/raw/master/bin/pyenv-installer | bash

# gitee 安装
curl -L https://gitee.com/xinghuipeng/pyenv-installer/raw/master/bin/pyenv-installer | bash

```

2.配置环境变量
```shell

echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.bashrc
echo 'export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.bashrc
echo -e 'if command -v pyenv 1>/dev/null 2>&1; then\n eval "$(pyenv init -)"\nfi' >> ~/.bashrc
```

3.刷新环境
```shell
exec $SHELL
```


## 安装python 

1.在安装python之前更新一下yum源

```shell
yum -y install libXcomposite libXtst gtk3 atk at-spi2-atk cups-libs libxkbcommon libXdamage libXrandr mesa-libgbm alsa-lib-devel
```

2.下载python
```shell
wget https://mirrors.huaweicloud.com/python/3.9.9/Python-3.9.9.tar.xz -P ~/.pyenv/cache
```

3.安装python
```shell
 pyenv install 3.9.9
```

4.安装python报错时安装所需环境
```shell
yum install gcc zlib-devel bzip2 bzip2-devel readline readline-devel sqlite sqlite-devel openssl openssl-devel git libffi-devel
yum install -y xz-devel
yum -y install libXcomposite libXtst gtk3 atk at-spi2-atk cups-libs libxkbcommon libXdamage libXrandr mesa-libgbm alsa-lib-devel
```

5.设置python全局环境
```shell
 pyenv global 3.9.9
```


## 部署程序
1.安装环境
程序解压到目录后
```shell
pip install -r requirements.txt 
```
2.如果启动urllib3报错，是因为urllib3版本过高
```shell
pip uninstall urllib3
pip install urllib3==1.21.1
```

3.下载chrome

1. 前往 http://dist.control.lth.se/public/CentOS-7/x86_64/google.x86_64/ 下载 google-chrome-stable-124.0.6367.118-1.x86_64.rpm版本

2. 上传到文件夹

3. 执行`sudo yum install lsb`
4. 执行 `yum localinstall google-chrome-stable_current_x86_64.rpm`
5. 执行 `google-chrome-stable --version`查看是否安装成功


4.执行程序
1. 进入到程序目录，执行`python driver.py`,如果控制台有输出则运行成功，如果有报错则需要解决报错
2. 执行python app.py启动服务



---
# **API 文档**
## **1. 搜索接口：`/search`**
### **请求方法**：
- `GET`
- `POST`

### **请求参数**：
| 参数   | 类型   | 描述               | 是否必填 | 示例           |
|--------|--------|--------------------|----------|----------------|
| `param`| `string` | 查询的关键字，搜索的内容 | 是       | `flask tutorial` |

### **成功响应：**

**状态码**: `200 OK`

#### **响应格式**：
```json
{
  "status": "success",
  "url": "https://www.google.com.hk/search?q=flask+tutorial",
  "message": "成功",
  "data": "49300000"
}
```

#### **字段说明**：
- `status`：请求状态，固定为 `"success"`。
- `url`：实际请求的 URL。
- `message`：成功时的消息，默认为 `"成功"`。
- `data`：搜索结果的数字数量（提取自页面文本）。

#### **示例**：
```json
{
  "status": "success",
  "url": "https://www.google.com.hk/search?q=flask+tutorial",
  "message": "成功",
  "data": "49300000"
}
```

### **错误响应：**

**状态码**: `400 Bad Request` 或 `500 Internal Server Error`

#### **错误响应格式**：

1. **参数缺失**：

如果请求没有提供 `param` 参数，返回错误响应：
```json
{
  "status": "error",
  "message": "Parameter param is required",
  "data": null
}
```

2. **无效的 XPath 结果**：

如果从 Google 搜索结果中无法提取到正确的匹配内容，返回如下错误响应：
```json
{
  "status": "error",
  "message": "fail: xpath result: 搜索结果未找到",
  "data": "base64_encoded_html_content",
  "code": 400
}
```

#### **字段说明**：
- `status`：请求状态，固定为 `"error"`。
- `message`：错误信息，包含问题描述。
- `data`：若请求出现错误时，返回的附加数据。若返回了 HTML 内容的 Base64 编码，则返回此数据，便于错误分析。
- `code`：HTTP 错误码，默认为 400，表示请求错误。

#### **示例**：

- **缺少 `param` 参数**：
```json
{
  "status": "error",
  "message": "Parameter param is required",
  "data": null
}
```

- **无效的 XPath 结果**：
```json
{
  "status": "error",
  "message": "fail: xpath result: 搜索结果未找到",
  "data": "base64_encoded_html_content",
  "code": 400
}
```

- **程序错误**：
```json
{
  "status": "error",
  "message": "程序报错的堆栈信息",
  "data": "Error",
  "code": 500
}
```
