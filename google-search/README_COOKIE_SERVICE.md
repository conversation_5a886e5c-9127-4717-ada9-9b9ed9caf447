# Google Cookie 服务

一个基于 Flask 的高效 Google Cookie 获取服务，提供 RESTful API 接口供其他应用使用。

## 特性

- 🚀 **高性能**: 使用无头浏览器，响应速度快
- 🔄 **实时更新**: 每次请求获取最新的 Cookie
- 💡 **资源优化**: 单例模式，最小化资源占用
- 🌐 **RESTful API**: 标准的 HTTP 接口
- 🛡️ **健壮性**: 完善的错误处理和日志记录
- 📊 **监控**: 内置健康检查和服务状态监控

## 快速开始

### 1. 安装依赖

```bash
# 自动安装所有依赖
python start_service.py --install

# 或手动安装
pip install -r requirements.txt
```

### 2. 启动服务

```bash
# 使用启动脚本（推荐）
python start_service.py

# 或直接启动
python flask_cookie_service.py
```

服务将在 `http://localhost:5000` 启动。

### 3. 测试服务

```bash
# 使用内置测试
python start_service.py --test

# 或使用客户端示例
python client_example.py
```

## API 接口

### 健康检查
```
GET /health
```

### 获取 Google Cookies
```
GET /api/cookies
GET /api/cookies?url=https://www.google.com/search?q=test
GET /api/cookies?fresh=true
```

### 获取搜索相关 Cookies
```
GET /api/cookies/search?q=python
```

### 获取 Cookie 字符串
```
GET /api/cookies/string
GET /api/cookies/string?fresh=true
```

### 服务信息
```
GET /api/info
```

## 使用示例

### cURL 示例

```bash
# 健康检查
curl http://localhost:5000/health

# 获取基础 Cookies
curl http://localhost:5000/api/cookies

# 获取全新会话 Cookies
curl "http://localhost:5000/api/cookies?fresh=true"

# 获取搜索 Cookies
curl "http://localhost:5000/api/cookies/search?q=python"

# 只获取 Cookie 字符串
curl http://localhost:5000/api/cookies/string
```

### Python 客户端示例

```python
from client_example import GoogleCookieClient

# 创建客户端
client = GoogleCookieClient("http://localhost:5000")

# 获取 Cookies
cookies = client.get_cookies()
if cookies["status"] == "success":
    cookie_string = cookies["data"]["cookie_string"]
    cookie_count = cookies["data"]["count"]
    print(f"获取到 {cookie_count} 个 Cookies")
    
    # 在 requests 中使用
    import requests
    headers = {"Cookie": cookie_string}
    response = requests.get("https://www.google.com", headers=headers)

# 获取搜索相关 Cookies
search_cookies = client.get_search_cookies("python programming")

# 获取全新会话 Cookies
fresh_cookies = client.get_cookies(fresh=True)

# 直接获取 Cookie 字符串
cookie_string = client.get_cookie_string()
```

### JavaScript 示例

```javascript
// 获取 Cookies
async function getCookies() {
    try {
        const response = await fetch('http://localhost:5000/api/cookies');
        const data = await response.json();
        
        if (data.status === 'success') {
            console.log(`获取到 ${data.data.count} 个 Cookies`);
            return data.data.cookie_string;
        }
    } catch (error) {
        console.error('获取 Cookies 失败:', error);
    }
}

// 使用 Cookie
getCookies().then(cookieString => {
    if (cookieString) {
        // 在请求中使用
        fetch('https://www.google.com', {
            headers: {
                'Cookie': cookieString
            }
        });
    }
});
```

## 响应格式

### 成功响应
```json
{
    "status": "success",
    "data": {
        "success": true,
        "cookies": [...],
        "cookie_string": "cookie1=value1; cookie2=value2",
        "count": 5,
        "timestamp": 1234567890,
        "user_agent": "Mozilla/5.0..."
    },
    "request_id": "req_1234567890"
}
```

### 错误响应
```json
{
    "status": "error",
    "error": "错误描述",
    "request_id": "req_1234567890"
}
```

## 配置选项

### 启动参数
```bash
python start_service.py --help

选项:
  --host HOST     服务主机地址 (默认: 0.0.0.0)
  --port PORT     服务端口 (默认: 5000)
  --debug         调试模式
  --install       安装依赖
  --test          测试服务
  --examples      显示使用示例
```

### 环境变量
- `FLASK_ENV`: 运行环境 (development/production)
- `FLASK_APP`: Flask 应用文件

## 性能优化

1. **无头模式**: 使用无头浏览器减少资源消耗
2. **单例模式**: 避免重复创建浏览器实例
3. **智能页面管理**: 每次请求使用新页面实例
4. **快速加载**: 只等待 DOM 加载完成，不等待所有资源
5. **连接复用**: 复用浏览器上下文

## 部署建议

### 开发环境
```bash
python start_service.py --debug
```

### 生产环境
```bash
# 使用 gunicorn (推荐)
pip install gunicorn
gunicorn --bind 0.0.0.0:5000 --workers 2 --timeout 60 flask_cookie_service:app

# 或使用内置服务器
python start_service.py --host 0.0.0.0 --port 5000
```

### Docker 部署
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 5000

CMD ["python", "flask_cookie_service.py"]
```

## 故障排除

### 常见问题

1. **服务启动失败**
   - 检查依赖是否安装: `python start_service.py --install`
   - 检查端口是否被占用: `lsof -i :5000`

2. **Cookie 获取失败**
   - 检查网络连接
   - 检查代理设置
   - 查看服务日志

3. **性能问题**
   - 增加超时时间
   - 检查系统资源使用情况
   - 考虑使用多个服务实例

### 日志查看
服务运行时会输出详细的日志信息，包括：
- 请求处理状态
- 错误信息
- 性能统计

## 安全注意事项

1. **网络安全**: 在生产环境中使用防火墙限制访问
2. **访问控制**: 考虑添加 API 密钥验证
3. **资源限制**: 设置请求频率限制
4. **日志安全**: 避免在日志中记录敏感信息

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站服务条款。
