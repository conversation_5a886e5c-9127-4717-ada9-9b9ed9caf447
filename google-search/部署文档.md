
# 部署
## 安装pyenv
1. 下载pyenv
```shell
# github
curl -L https://github.com/pyenv/pyenv-installer/raw/master/bin/pyenv-installer | bash

# gitee 安装
curl -L https://gitee.com/xinghuipeng/pyenv-installer/raw/master/bin/pyenv-installer | bash

```

2.配置环境变量
```shell

echo 'export PYENV_ROOT="$HOME/.pyenv"' >> ~/.bashrc
echo 'export PATH="$PYENV_ROOT/bin:$PATH"' >> ~/.bashrc
echo -e 'if command -v pyenv 1>/dev/null 2>&1; then\n eval "$(pyenv init -)"\nfi' >> ~/.bashrc
```

3.刷新环境
```shell
exec $SHELL
```


## 安装python 

1.在安装python之前更新一下yum源

```shell
yum -y install libXcomposite libXtst gtk3 atk at-spi2-atk cups-libs libxkbcommon libXdamage libXrandr mesa-libgbm alsa-lib-devel
```

2.下载python
```shell
wget https://mirrors.huaweicloud.com/python/3.9.9/Python-3.9.9.tar.xz -P ~/.pyenv/cache
```

3.安装python
```shell
 pyenv install 3.9.9
```

4.安装python报错时安装所需环境
```shell
yum install gcc zlib-devel bzip2 bzip2-devel readline readline-devel sqlite sqlite-devel openssl openssl-devel git libffi-devel
yum install -y xz-devel
yum -y install libXcomposite libXtst gtk3 atk at-spi2-atk cups-libs libxkbcommon libXdamage libXrandr mesa-libgbm alsa-lib-devel
```

5.设置python全局环境
```shell
 pyenv global 3.9.9
```


## 部署程序
1.安装环境
程序解压到目录后
```shell
pip install -r requirements.txt 
```
2.如果启动urllib3报错，是因为urllib3版本过高
```shell
pip uninstall urllib3
pip install urllib3==1.21.1
```

3.下载chrome

1. 前往 http://dist.control.lth.se/public/CentOS-7/x86_64/google.x86_64/ 下载 `google-chrome-stable-124.0.6367.118-1.x86_64.rpm`版本

2. 上传到文件夹

3. 执行`sudo yum install lsb`
4. 执行 `yum localinstall google-chrome-stable_current_x86_64.rpm`
5. 执行 `google-chrome-stable --version`查看是否安装成功


4.执行程序
程序目录在`/home/<USER>
1. 进入到程序目录，执行`python get_google_cookie.py`,如果控制台有输出则运行成功，如果有报错则需要解决报错


