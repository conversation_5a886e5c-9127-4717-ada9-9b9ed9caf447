import os
import time
import traceback
import json
from typing import Dict, List, Optional
from patchright.sync_api import sync_playwright, Play<PERSON><PERSON><PERSON>x<PERSON><PERSON>ana<PERSON>


def singleton(cls):
    """
    将装饰的类替换成一个“获取单例”的函数。
    """
    instances = {}

    def getinstance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return getinstance


@singleton
class GoogleCookieService:
    """
    高效的 Google Cookie 获取服务
    - 保证每次获取都是新的 Cookie
    - 资源利用最小化
    - 快速响应
    """

    def __init__(self,
                 user_agent: str = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"):
        self.user_agent = user_agent
        self.playwright = sync_playwright().start()
        self.context = self.playwright.chromium.launch_persistent_context(
            user_data_dir="...",
            channel="chrome",
            headless=True,  # 改为无头模式提高性能
            no_viewport=True,

        )
        # 预热一个页面，但不立即使用
        self._base_page = None

    def _get_fresh_page(self):
        """获取一个新的页面实例"""
        if self._base_page:
            try:
                self._base_page.close()
            except:
                pass
        self._base_page = self.context.new_page()
        return self._base_page

    def get_google_cookies(self, target_url: str = "https://www.google.com") -> Dict:
        """
        获取 Google 的 Cookie

        Args:
            target_url: 目标 Google URL，默认为主页

        Returns:
            包含 cookies 和相关信息的字典
        """
        try:
            # 获取新的页面实例
            page = self._get_fresh_page()

            # 设置用户代理
            page.set_extra_http_headers({
                'User-Agent': self.user_agent
            })

            # 访问 Google 页面
            page.goto(target_url, wait_until="domcontentloaded", timeout=10000)

            # 等待页面基本加载完成（不等待所有资源）
            page.wait_for_load_state("domcontentloaded")

            # 获取所有 cookies
            cookies = page.context.cookies()

            # 过滤出 Google 相关的 cookies
            google_cookies = [
                cookie for cookie in cookies
                if 'google' in cookie.get('domain', '').lower()
            ]

            # 格式化 cookies 为字符串格式（常用格式）
            cookie_string = "; ".join([
                f"{cookie['name']}={cookie['value']}"
                for cookie in google_cookies
            ])

            return {
                "success": True,
                "cookies": google_cookies,
                "cookie_string": cookie_string,
                "count": len(google_cookies),
                "timestamp": int(time.time()),
                "user_agent": self.user_agent
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": int(time.time())
            }

    def get_cookies_for_search(self, search_query: str = "test") -> Dict:
        """
        通过执行搜索获取 Google 搜索相关的 Cookie

        Args:
            search_query: 搜索关键词

        Returns:
            包含 cookies 和相关信息的字典
        """
        search_url = f"https://www.google.com/search?q={search_query}"
        return self.get_google_cookies(search_url)

    def get_fresh_session_cookies(self) -> Dict:
        """
        获取全新会话的 Cookie（清除现有状态）

        Returns:
            包含 cookies 和相关信息的字典
        """
        try:
            # 清除现有的 cookies 和存储
            self.context.clear_cookies()
            self.context.clear_permissions()

            # 获取新的 cookies
            return self.get_google_cookies()

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to get fresh session: {str(e)}",
                "timestamp": int(time.time())
            }

    def health_check(self) -> Dict:
        """
        服务健康检查

        Returns:
            服务状态信息
        """
        try:
            # 简单的连接测试
            page = self._get_fresh_page()
            page.goto("https://www.google.com", timeout=5000)

            return {
                "status": "healthy",
                "timestamp": int(time.time()),
                "context_active": True
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": int(time.time()),
                "context_active": False
            }

    def close(self):
        """关闭浏览器上下文和 Playwright"""
        try:
            if self._base_page:
                self._base_page.close()
            self.context.close()
            self.playwright.stop()
        except Exception as e:
            print(f"Error during cleanup: {e}")


# 便捷的全局实例
google_cookie_service = GoogleCookieService()


def get_google_cookies_quick(target_url: str = "https://www.google.com") -> Dict:
    """
    快速获取 Google Cookies 的便捷函数

    Args:
        target_url: 目标 URL

    Returns:
        Cookie 信息字典
    """
    return google_cookie_service.get_google_cookies(target_url)


if __name__ == "__main__":
    # 测试服务
    service = GoogleCookieService()

    print("=== 健康检查 ===")
    health = service.health_check()
    print(json.dumps(health, indent=2, ensure_ascii=False))


    print("\n=== 获取搜索 Cookies ===")
    search_cookies = service.get_cookies_for_search("python")
    print(f"成功: {search_cookies}")
    if search_cookies['success']:
        print(f"搜索 Cookie 数量: {search_cookies['count']}")

    print("\n=== 获取全新会话 Cookies ===")
    fresh_cookies = service.get_fresh_session_cookies()
    print(f"成功: {fresh_cookies['success']}")
    if fresh_cookies['success']:
        print(f"全新会话 Cookie 数量: {fresh_cookies['count']}")

    # 清理资源
    service.close()
