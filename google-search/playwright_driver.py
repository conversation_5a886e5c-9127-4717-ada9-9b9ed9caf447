import os
import time
import traceback
from patchright.sync_api import sync_playwright, <PERSON>wright<PERSON><PERSON>xtManager


def singleton(cls):
    """
    将装饰的类替换成一个“获取单例”的函数。
    """
    instances = {}

    def getinstance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return getinstance


@singleton
class Google:

    def __init__(self,
                 user_agent: str = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36"):
        self.user_agent = user_agent
        self.playwright = sync_playwright().start()
        self.context = self.playwright.chromium.launch_persistent_context(
            user_data_dir="...",
            channel="chrome",
            headless=False,
            no_viewport=True,
            proxy={
                "server": "pr.roxlabs.cn:4600",
                "username": "user-c4r1rr-region-hk",
                "password": "RR3QI32CQK"
            }
        )
        self.page = self.context.new_page()

    def create_driver(self):
        # 如果你还想在后面复用 driver/context：
        return self.context

    def start_login(self):
        # 不再用 driver.new_page()，改用 context
        page = self.context.new_page()
        page.goto(
            "https://www.google.com/search?q=qq"
        )
        page.wait_for_load_state("networkidle")
        print(page.content())
        return page  # 如果后面还想拿到 page

    def close(self):
        self.context.close()
        self.playwright.stop()


if __name__ == "__main__":
    login = Login()
    login.start_login()
    login.close()
