#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
import time
import argparse

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import flask
        import flask_cors
        import patchright
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def install_dependencies():
    """安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False


def start_service(host="0.0.0.0", port=5000, debug=False):
    """启动服务"""
    print(f"正在启动 Google Cookie 服务...")
    print(f"地址: http://{host}:{port}")
    
    # 设置环境变量
    env = os.environ.copy()
    env['FLASK_APP'] = 'flask_cookie_service.py'
    env['FLASK_ENV'] = 'development' if debug else 'production'
    
    try:
        # 启动 Flask 应用
        if debug:
            subprocess.run([
                sys.executable, "flask_cookie_service.py"
            ], env=env)
        else:
            # 生产模式使用 gunicorn（如果可用）
            try:
                subprocess.run([
                    "gunicorn", 
                    "--bind", f"{host}:{port}",
                    "--workers", "2",
                    "--timeout", "60",
                    "flask_cookie_service:app"
                ], env=env)
            except FileNotFoundError:
                # 如果没有 gunicorn，使用内置服务器
                print("⚠️  未找到 gunicorn，使用内置服务器")
                subprocess.run([
                    sys.executable, "flask_cookie_service.py"
                ], env=env)
                
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def test_service(host="localhost", port=5000):
    """测试服务"""
    print("正在测试服务...")
    
    base_url = f"http://{host}:{port}"
    
    # 测试健康检查
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            print("✅ 健康检查通过")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        return False
    
    # 测试获取 Cookies
    try:
        response = requests.get(f"{base_url}/api/cookies", timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get("status") == "success":
                cookie_count = data["data"]["count"]
                print(f"✅ Cookie 获取测试通过，获取到 {cookie_count} 个 Cookies")
            else:
                print(f"❌ Cookie 获取失败: {data.get('error')}")
                return False
        else:
            print(f"❌ Cookie 获取请求失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cookie 获取测试失败: {e}")
        return False
    
    print("✅ 所有测试通过")
    return True

def show_usage_examples():
    """显示使用示例"""
    print("""
=== 使用示例 ===

1. 健康检查:
   curl http://localhost:5000/health

2. 获取基础 Cookies:
   curl http://localhost:5000/api/cookies

3. 获取全新会话 Cookies:
   curl "http://localhost:5000/api/cookies?fresh=true"

4. 获取搜索 Cookies:
   curl "http://localhost:5000/api/cookies/search?q=python"

5. 只获取 Cookie 字符串:
   curl http://localhost:5000/api/cookies/string

6. 获取服务信息:
   curl http://localhost:5000/api/info

=== Python 客户端示例 ===

from client_example import GoogleCookieClient

client = GoogleCookieClient("http://localhost:5000")
cookies = client.get_cookies()
if cookies["status"] == "success":
    cookie_string = cookies["data"]["cookie_string"]
    print(f"获取到 {cookies['data']['count']} 个 Cookies")
""")

def main():
    parser = argparse.ArgumentParser(description="Google Cookie 服务启动器")
    parser.add_argument("--host", default="0.0.0.0", help="服务主机地址")
    parser.add_argument("--port", type=int, default=8090, help="服务端口")
    parser.add_argument("--debug", action="store_true", help="调试模式")
    parser.add_argument("--install", action="store_true", help="安装依赖")
    parser.add_argument("--test", action="store_true", help="测试服务")
    parser.add_argument("--examples", action="store_true", help="显示使用示例")
    
    args = parser.parse_args()
    
    if args.examples:
        show_usage_examples()
        return
    
    if args.install:
        if install_dependencies():
            print("✅ 依赖安装完成，可以启动服务了")
        return
    
    if args.test:
        if test_service(args.host, args.port):
            print("✅ 服务测试通过")
        else:
            print("❌ 服务测试失败")
        return
    
    # 检查依赖
    if not check_dependencies():
        print("请先安装依赖: python start_service.py --install")
        return
    
    print("🚀 Google Cookie 服务启动器")
    print("=" * 50)
    
    # 启动服务
    start_service(args.host, args.port, args.debug)

if __name__ == "__main__":
    main()
