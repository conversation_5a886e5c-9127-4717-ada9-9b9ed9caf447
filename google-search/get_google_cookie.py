import os
import string

from flask import Flask, jsonify
from DrissionPage import ChromiumOptions, ChromiumPage

app = Flask(__name__)


def from_proxy(proxy):
    """
    格式化代理
    :param proxy:
    :return:
    """
    # proxy是这种格式 user:pass@ip:port
    user_pass_str, ip_port_str = proxy.split('@')
    proxyHost, proxyPort = ip_port_str.split(':')
    proxyUser, proxyPass = user_pass_str.split(':')
    return proxyHost, proxyPort, proxyUser, proxyPass




def create_proxy_auth_extension(proxy_host, proxy_port, proxy_username, proxy_password, scheme='http',
                                plugin_path=None):
    """
    用来设置带用户名密码的代理，在本地中实现一个插件，加载到服务器中
    :param proxy_host:
    :param proxy_port:
    :param proxy_username:
    :param proxy_password:
    :param scheme:
    :param plugin_path:
    :return:
    """
    # 创建Chrome插件的manifest.json文件内容
    manifest_json = """
    {
        "version": "1.0.0",
        "manifest_version": 2,
        "name": "16YUN Proxy",
        "permissions": [
            "proxy",
            "tabs",
            "unlimitedStorage",
            "storage",
            "<all_urls>",
            "webRequest",
            "webRequestBlocking"
        ],
        "background": {
            "scripts": ["background.js"]
        },
        "minimum_chrome_version":"22.0.0"
    }
    """

    # 创建Chrome插件的background.js文件内容
    background_js = string.Template(
        """
        var config = {
            mode: "fixed_servers",
            rules: {
                singleProxy: {
                    scheme: "${scheme}",
                    host: "${host}",
                    port: parseInt(${port})
                },
                bypassList: ["localhost"]
            }
        };

        chrome.proxy.settings.set({value: config, scope: "regular"}, function() {});

        function callbackFn(details) {
            return {
                authCredentials: {
                    username: "${username}",
                    password: "${password}"
                }
            };
        }

        chrome.webRequest.onAuthRequired.addListener(
            callbackFn,
            {urls: ["<all_urls>"]},
            ['blocking']
        );
        """
    ).substitute(
        host=proxy_host,
        port=proxy_port,
        username=proxy_username,
        password=proxy_password,
        scheme=scheme,
    )

    # 创建插件目录并写入manifest.json和background.js文件
    os.makedirs(plugin_path, exist_ok=True)
    with open(os.path.join(plugin_path, "manifest.json"), "w+") as f:
        f.write(manifest_json)
    with open(os.path.join(plugin_path, "background.js"), "w+") as f:
        f.write(background_js)

    return os.path.join(plugin_path)

def get_google_cookie():
    try:
        print("开始配置浏览器...")
        # 创建浏览器选项
        option = ChromiumOptions()

        proxyHost, proxyPort, proxyUser, proxyPass = from_proxy("juming:juming123456...@*************:32188")
        proxy_auth_plugin_path = create_proxy_auth_extension(
            plugin_path="/tmp/111",
            proxy_host=proxyHost,
            proxy_port=proxyPort,
            proxy_username=proxyUser,
            proxy_password=proxyPass
        )


        # option.set_proxy('127.0.0.1:7890')  # 设置代理
        option.auto_port()  # 自动设置端口
        
        # 创建页面对象
        option.set_browser_path(r'/opt/google/chrome/google-chrome')  # 设置chrome浏览器路径
        option.set_argument("--headless")  # 设置为无头模式，隐藏浏览器界面
        option.set_argument('--no-sandbox')
        option.set_argument("--disable-gpu") # 不实用gpu
        option.set_argument("--disable-blink-features=AutomationControlled")
        option.set_user_agent(
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/124.0.6367.118 Safari/537.36") # 当设置为无头模式的时候需要设置请求头
        option.add_extension(path=proxy_auth_plugin_path) #添加插件
        page = ChromiumPage(option)

        try:
            print("\n正在访问 Google...")
            page.get('https://www.google.com/search?client=aff-cs-360se&ie=UTF-8&q=site%3Aqq.com&oq=site%3Aqq.com&aqs=chrome..69i57j69i59l2j69i60j69i58j69i60l2.5669j0j1&')
            
            print("等待页面加载...")
            # print("\n=== Page 对象详细信息 ===")
            # print("Page 类型:", type(page))
            # print("Page 属性和方法:", dir(page))
            # print("Wait 对象属性和方法:", dir(page.wait))
            # 等待页面文档加载完成
            page.wait.doc_loaded()
          
            print("\n=== Cookies 信息 ===")
            print("page.cookies() 值:", page.cookies())
            print("\n获取 Cookies...")
            # 调用 cookies() 方法获取 cookies
            all_cookies = page.cookies()  # 作为方法调用
            if not all_cookies:
                return {"status": "error", "message": "未获取到任何 Cookie"}
            cookie_string = '; '.join([f"{cookie['name']}={cookie['value']}" for cookie in all_cookies])
            return {"status": "success", "cookie": cookie_string}
        finally:
            print("关闭浏览器...")
            page.quit()
    except Exception as e:
        return {"status": "error", "message": str(e)}

@app.route('/get_cookie', methods=['GET'])
def cookie_api():
    result = get_google_cookie()
    return jsonify(result)

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5001)